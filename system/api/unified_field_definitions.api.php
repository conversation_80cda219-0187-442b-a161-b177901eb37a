<?php

namespace api\unified_field_definitions;

use system\unified_field_definitions;
use system\unified_field_mapper;
use edge\Edge;
/**
 * API endpoints for managing unified field definitions
 */

/**
 * Get all field definitions
 */
function index($params) {
    try {
        $fields = unified_field_definitions::get_all_fields();
        $stats = unified_field_definitions::get_field_statistics();

        return json_encode([
            'success' => true,
            'fields' => $fields,
            'statistics' => $stats
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        return json_encode([
            'success' => false,
            'error' => 'Failed to load field definitions: ' . $e->getMessage()
        ]);
    }
}

/**
 * Get single field definition
 */
function get_field($params) {
    try {
        $field_name = $params['field_name'] ?? '';
        if (empty($field_name)) {
            throw new Exception('Field name is required');
        }

        $field = unified_field_definitions::get_field($field_name);
        if (!$field) {
            throw new Exception('Field not found');
        }

        return json_encode([
            'success' => true,
            'field' => $field,
            'is_custom' => unified_field_definitions::is_custom_field($field_name),
            'is_modified' => unified_field_definitions::is_modified_field($field_name)
        ]);
    } catch (Exception $e) {
        http_response_code(404);
        return json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Create new field definition
 */
function create($params) {
    try {
        $field_name = $params['field_name'] ?? '';

        if (empty($field_name)) {
            throw new Exception('Field name is required');
        }

        // Check if field already exists
        if (unified_field_definitions::get_field($field_name)) {
            throw new Exception('Field already exists');
        }

        // Build definition from form data
        $definition = [
            'label' => $params['label'] ?? '',
            'type' => $params['type'] ?? 'string',
            'category' => $params['category'] ?? 'custom',
            'description' => $params['description'] ?? '',
            'patterns' => array_filter(explode("\n", $params['patterns'] ?? ''), 'trim'),
            'normalized_fields' => array_filter(explode("\n", $params['normalized_fields'] ?? ''), 'trim'),
            'validation' => [
                'required' => false
            ],
            'matching' => [
                'enabled' => isset($params['matching_enabled']),
                'priority' => (int)($params['matching_priority'] ?? 10),
                'case_sensitive' => isset($params['case_sensitive']),
                'fuzzy_matching' => isset($params['fuzzy_matching']),
                'similarity_threshold' => (int)($params['similarity_threshold'] ?? 70),
                'exact_match_only' => !isset($params['fuzzy_matching'])
            ]
        ];

        // Validate definition
        $validation = unified_field_definitions::validate_field_definition($definition);
        if (!$validation['valid']) {
            throw new Exception('Invalid field definition: ' . implode(', ', $validation['errors']));
        }

        // Save field definition
        $success = unified_field_definitions::save_field_definition($field_name, $definition);
        if (!$success) {
            throw new Exception('Failed to save field definition');
        }

        // Return success message and close modal with OOB swap to refresh field definitions
        header('HX-Trigger: {"closeModal": true, "showNotification": {"type": "success", "message": "Field definition created successfully"}}');

        // Return the updated field definitions section as OOB swap
        $field_definitions_html = edge::render('field-definitions-section');

        return '<div hx-swap-oob="outerHTML:#field-definitions-section">' . $field_definitions_html . '</div>' .
               '<div class="p-4 bg-green-50 border border-green-200 rounded-md">' .
               '<p class="text-green-800">Successfully created field definition</p>' .
               '</div>';

    } catch (Exception $e) {
        // Return error message in modal
        $error_html = '<div class="p-4 bg-red-50 border border-red-200 rounded-md mb-4">';
        $error_html .= '<p class="text-red-800">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        $error_html .= '</div>';

        // Re-include the form with error
        $field = unified_field_definitions::create_field_template($field_name ?? 'new_field');
        $categories = unified_field_definitions::get_categories();
        $types = ['string', 'email', 'date', 'number', 'currency'];
        $is_new = true;
        $form_title = 'Create New Field Definition';
        $submit_action = 'create';

        ob_start();
        echo $error_html;
        include __DIR__ . '/../views/system/field_definition_edit_form.view.php';
        return ob_get_clean();
    }
}

/**
 * Update field definition
 */
function update($params) {
    try {
        $field_name = $params['field_name'] ?? '';

        if (empty($field_name)) {
            throw new Exception('Field name is required');
        }

        // Build definition from form data
        $definition = [
            'label' => $params['label'] ?? '',
            'type' => $params['type'] ?? 'string',
            'category' => $params['category'] ?? 'custom',
            'description' => $params['description'] ?? '',
            'patterns' => array_filter(explode("\n", $params['patterns'] ?? ''), 'trim'),
            'normalized_fields' => array_filter(explode("\n", $params['normalized_fields'] ?? ''), 'trim'),
            'validation' => [
                'required' => false
            ],
            'matching' => [
                'enabled' => isset($params['matching_enabled']),
                'priority' => (int)($params['matching_priority'] ?? 10),
                'case_sensitive' => isset($params['case_sensitive']),
                'fuzzy_matching' => isset($params['fuzzy_matching']),
                'similarity_threshold' => (int)($params['similarity_threshold'] ?? 70),
                'exact_match_only' => !isset($params['fuzzy_matching'])
            ]
        ];

        // Validate definition
        $validation = unified_field_definitions::validate_field_definition($definition);
        if (!$validation['valid']) {
            throw new Exception('Invalid field definition: ' . implode(', ', $validation['errors']));
        }

        // Save field definition
        $success = unified_field_definitions::save_field_definition($field_name, $definition);
        if (!$success) {
            throw new Exception('Failed to update field definition');
        }

        // Return success message and close modal with OOB swap to refresh field definitions
        header('HX-Trigger: {"closeModal": true, "showNotification": {"type": "success", "message": "Field definition updated successfully"}}');

        // Return the updated field definitions section as OOB swap
        $field_definitions_html = edge::render('field-definitions-section');

        return '<div hx-swap-oob="outerHTML:#field-definitions-section">' . $field_definitions_html . '</div>' .
               '<div class="p-4 bg-green-50 border border-green-200 rounded-md">' .
               '<p class="text-green-800">Successfully updated field definition</p>' .
               '</div>';

    } catch (Exception $e) {
        // Return error message in modal
        $error_html = '<div class="p-4 bg-red-50 border border-red-200 rounded-md mb-4">';
        $error_html .= '<p class="text-red-800">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        $error_html .= '</div>';

        // Re-include the form with error
        $field = unified_field_definitions::get_field($field_name);
        $categories = unified_field_definitions::get_categories();
        $types = ['string', 'email', 'date', 'number', 'currency'];
        $is_new = false;
        $form_title = 'Edit Field Definition: ' . $field_name;
        $submit_action = 'update';

        ob_start();
        echo $error_html;
        include __DIR__ . '/../views/system/field_definition_edit_form.view.php';
        return ob_get_clean();
    }
}

/**
 * Delete field definition
 */
function delete($params) {
    try {
        $field_name = $params['field_name'] ?? '';

        if (empty($field_name)) {
            throw new Exception('Field name is required');
        }

        // Check if field exists
        if (!unified_field_definitions::get_field($field_name)) {
            throw new Exception('Field not found');
        }

        // Delete field definition
        $success = unified_field_definitions::delete_field_definition($field_name);
        if (!$success) {
            throw new Exception('Failed to delete field definition');
        }

        return json_encode([
            'success' => true,
            'message' => 'Field definition deleted successfully'
        ]);
    } catch (Exception $e) {
        http_response_code(400);
        return json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Reset field to default
 */
function reset($params) {
    try {
        $field_name = $params['field_name'] ?? '';

        if (empty($field_name)) {
            throw new Exception('Field name is required');
        }

        // Reset field definition
        $success = unified_field_definitions::reset_field_to_default($field_name);
        if (!$success) {
            throw new Exception('Failed to reset field definition');
        }

        // Return success message and close modal
        return '<div class="p-4 bg-green-50 border border-green-200 rounded-md">' .
            '<p class="text-green-800">Successfully reset field definition</p>' .
            '</div>';

    } catch (Exception $e) {
        // Return error message
        return '<div class="p-4 bg-red-50 border border-red-200 rounded-md">' .
               '<p class="text-red-800">Error: ' . htmlspecialchars($e->getMessage()) . '</p>' .
               '</div>';
    }
}

/**
 * Get field editor form
 */
function edit_form($params) {
    try {
        $field_name = $params['field_name'] ?? '';
        $is_new = $params['is_new'] ?? false;

        if (!$is_new && empty($field_name)) {
            throw new Exception('Field name is required');
        }

        $field = null;
        if (!$is_new) {
            $field = unified_field_definitions::get_field($field_name);
            if (!$field) {
                throw new Exception('Field not found');
            }
        } else {
            $field = unified_field_definitions::create_field_template('new_field');
        }

        $categories = unified_field_definitions::get_categories();
        $types = ['string', 'email', 'date', 'number', 'currency'];
        $form_title = $is_new ? 'Create New Field Definition' : 'Edit Field Definition: ' . $field_name;
        $submit_action = $is_new ? 'create' : 'update';

        return edge::render('field-definition-edit-form', [
            'field' => $field,
            'field_name' => $field_name,
            'categories' => $categories,
            'types' => $types,
            'form_title' => $form_title,
            'submit_action' => $submit_action
        ]);


    } catch (Exception $e) {
        return '<div class="p-4 bg-red-50 border border-red-200 rounded-md">' .
               '<p class="text-red-800">Error: ' . htmlspecialchars($e->getMessage()) . '</p>' .
               '</div>';
    }
}

/**
 * Update field priority and matching settings
 */
function update_field_priority($params = []) {
    try {
        $field_name = $params['field_name'] ?? '';
        $field_priorities = $params['field_priorities'] ?? [];

        if (empty($field_name) && empty($field_priorities)) {
            throw new Exception("Field name or field priorities data is required");
        }

        $updated_count = 0;
        $errors = [];

        // Handle single field update
        if (!empty($field_name)) {
            $priority = $params['priority'] ?? null;
            $enabled = $params['enabled'] ?? null;
            $confidence_threshold = $params['confidence_threshold'] ?? null;

            $result = update_single_field_priority($field_name, $priority, $enabled, $confidence_threshold);
            if ($result['success']) {
                $updated_count++;
            } else {
                $errors[] = $result['error'];
            }
        }

        // Handle bulk field updates
        if (!empty($field_priorities)) {
            foreach ($field_priorities as $field_name => $settings) {
                $priority = $settings['priority'] ?? null;
                $enabled = isset($settings['enabled']) ? (bool)$settings['enabled'] : null;
                $confidence_threshold = $settings['confidence_threshold'] ?? null;
                $exact_match_only = isset($settings['exact_match_only']) ? (bool)$settings['exact_match_only'] : null;
                $fuzzy_matching = isset($settings['fuzzy_matching']) ? (bool)$settings['fuzzy_matching'] : null;

                $result = update_single_field_priority($field_name, $priority, $enabled, $confidence_threshold, $exact_match_only, $fuzzy_matching);
                if ($result['success']) {
                    $updated_count++;
                } else {
                    $errors[] = "Field {$field_name}: " . $result['error'];
                }
            }
        }

        // Return status message for UI with OOB swap if successful
        if ($updated_count > 0) {
            $message = $updated_count === 1 ?
                "Field priority updated successfully" :
                "{$updated_count} field priorities updated successfully";

            if (!empty($errors)) {
                $message .= " (with " . count($errors) . " errors)";
            }

            // Return the updated field definitions section as OOB swap
            $field_definitions_html = edge::render('field-definitions-section');

            return '<div hx-swap-oob="outerHTML:#field-definitions-section">' . $field_definitions_html . '</div>' .
                   '<div class="text-sm text-green-700 bg-green-100 px-3 py-2 rounded">' . $message . '</div>';
        } else {
            $error_message = !empty($errors) ? implode(', ', $errors) : 'No fields were updated';
            return '<div class="text-sm text-red-700 bg-red-100 px-3 py-2 rounded">Error: ' . $error_message . '</div>';
        }

    } catch (Exception $e) {
        return '<div class="text-sm text-red-700 bg-red-100 px-3 py-2 rounded">Error: ' . $e->getMessage() . '</div>';
    }
}

/**
 * Update a single field's priority and settings
 */
function update_single_field_priority($field_name, $priority = null, $enabled = null, $confidence_threshold = null, $exact_match_only = null, $fuzzy_matching = null) {
    try {
        // Get current field definition
        $current_definition = unified_field_definitions::get_field($field_name);
        if (!$current_definition) {
            throw new Exception("Field '{$field_name}' not found");
        }

        // Update matching configuration
        $matching_config = $current_definition['matching'] ?? [];

        if ($priority !== null) {
            $matching_config['priority'] = (int)$priority;
        }
        if ($enabled !== null) {
            $matching_config['enabled'] = (bool)$enabled;
        }
        if ($confidence_threshold !== null) {
            $matching_config['confidence_threshold'] = (int)$confidence_threshold;
        }
        if ($exact_match_only !== null) {
            $matching_config['exact_match_only'] = (bool)$exact_match_only;
        }
        if ($fuzzy_matching !== null) {
            $matching_config['fuzzy_matching'] = (bool)$fuzzy_matching;
        }

        // Update the definition
        $updated_definition = $current_definition;
        $updated_definition['matching'] = $matching_config;

        // Save to database
        $success = unified_field_definitions::save_field_definition($field_name, $updated_definition);

        return [
            'success' => $success,
            'error' => $success ? null : "Failed to save field definition"
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Reset all field priorities to defaults
 */
function reset_all_priorities($params = []) {
    try {
        // Get all custom field definitions
        $all_fields = unified_field_definitions::get_all_fields();
        $reset_count = 0;
        $errors = [];

        foreach ($all_fields as $field_name => $definition) {
            // Check if this field has been customized
            if (unified_field_definitions::is_modified_field($field_name)) {
                $success = unified_field_definitions::reset_field_to_default($field_name);
                if ($success) {
                    $reset_count++;
                } else {
                    $errors[] = "Failed to reset field: {$field_name}";
                }
            }
        }

        if ($reset_count > 0) {
            $message = "{$reset_count} field(s) reset to default priorities";
            if (!empty($errors)) {
                $message .= " (with " . count($errors) . " errors)";
            }

            // Return the updated field definitions section as OOB swap
            $field_definitions_html = edge::render('field-definitions-section');

            return '<div hx-swap-oob="outerHTML:#field-definitions-section">' . $field_definitions_html . '</div>' .
                   '<div class="text-sm text-green-700 bg-green-100 px-3 py-2 rounded">' . $message . '</div>';
        } else {
            return '<div class="text-sm text-blue-700 bg-blue-100 px-3 py-2 rounded">No custom field priorities found to reset</div>';
        }

    } catch (Exception $e) {
        return '<div class="text-sm text-red-700 bg-red-100 px-3 py-2 rounded">Error: ' . $e->getMessage() . '</div>';
    }
}

/**
 * Test field matching for a given column name
 */
function test_field_matching($params = []) {
    try {
        $column_name = $params['column_name'] ?? '';
        $context_columns = $params['context_columns'] ?? [];

        if (empty($column_name)) {
            throw new Exception("Column name is required");
        }

        // Convert context_columns string to array if needed
        if (is_string($context_columns)) {
            $context_columns = array_filter(array_map('trim', explode(',', $context_columns)));
        }

        $suggestions = \system\unified_field_mapper::suggest_field_mappings([$column_name]);
        $suggestion = $suggestions[$column_name] ?? null;

        return json_encode([
            'success' => true,
            'column_name' => $column_name,
            'context_columns' => $context_columns,
            'suggestion' => $suggestion,
            'has_match' => !empty($suggestion)
        ]);

    } catch (Exception $e) {
        return json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Clear field mapper cache to force reload from database
 */
function clear_cache($params = []) {
    try {
        unified_field_mapper::clear_cache();

        return json_encode([
            'success' => true,
            'message' => 'Field mapper cache cleared successfully'
        ]);

    } catch (Exception $e) {
        return json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Get field definitions section component for OOB swap
 */
function get_field_definitions_section($params = []) {
    try {
        return edge::render('field-definitions-section');

    } catch (Exception $e) {
        return '<div class="p-4 bg-red-50 border border-red-200 rounded-md">' .
               '<p class="text-red-800">Error loading field definitions: ' . htmlspecialchars($e->getMessage()) . '</p>' .
               '</div>';
    }
}
