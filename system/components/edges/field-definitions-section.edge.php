<?php
/**
 * Field Definitions Section Component
 * 
 * Displays the available field definitions with category tabs and field cards
 * Can be used as an OOB swap target when field definitions are updated
 */

use system\unified_field_definitions;

// Get field definitions and categories
$all_fields = unified_field_definitions::get_all_fields();
$categories = unified_field_definitions::get_categories();
?>

<div id="field-definitions-section" class="mb-8">
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">Available Field Definitions</h2>
                    <p class="mt-1 text-sm text-gray-600">
                        Centralized field definitions used across subscription matching, data importing, and other systems.
                    </p>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="text-sm text-gray-500"><?= count($all_fields) ?> fields defined</span>
                    <button type="button"
                            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                            hx-get="<?= APP_ROOT ?>/api/unified_field_definitions/edit_form?is_new=true"
                            hx-target="body"
                            hx-swap="beforeend">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        New Field
                    </button>
                </div>
            </div>
        </div>

        <div class="px-6 py-4" x-data="{ activeCategory: '<?= $categories[0] ?? 'identification' ?>' }">
            <!-- Category Tabs -->
            <div class="border-b border-gray-200 mb-4">
                <nav class="-mb-px flex space-x-8">
                    <?php foreach ($categories as $index => $category): ?>
                        <button type="button"
                                class="py-2 px-1 border-b-2 font-medium text-sm transition-colors"
                                :class="activeCategory === '<?= $category ?>' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                @click="activeCategory = '<?= $category ?>'">
                            <?= ucwords(str_replace('_', ' ', $category)) ?>
                        </button>
                    <?php endforeach; ?>
                </nav>
            </div>

            <!-- Field Definitions by Category -->
            <?php foreach ($categories as $index => $category): ?>
                <div x-show="activeCategory === '<?= $category ?>'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <?php
                        $category_fields = unified_field_definitions::get_fields_by_category($category);
                        foreach ($category_fields as $field_name => $definition):
                            $is_custom = unified_field_definitions::is_custom_field($field_name);
                            $is_modified = unified_field_definitions::is_modified_field($field_name);
                        ?>
                            <div class="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="font-medium text-gray-900"><?= $definition['label'] ?></h4>
                                    <div class="flex items-center space-x-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                            <?= $definition['type'] ?>
                                        </span>
                                        <button type="button"
                                                class="text-indigo-600 hover:text-indigo-900 text-xs"
                                                hx-get="<?= APP_ROOT ?>/api/unified_field_definitions/edit_form?field_name=<?= urlencode($field_name) ?>"
                                                hx-target="#modal_body"
                                                @click="showModal = true"
                                                title="Edit field definition">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <p class="text-sm text-gray-600 mb-3"><?= $definition['description'] ?></p>

                                <!-- Status Indicators -->
                                <?php if ($is_custom || $is_modified): ?>
                                    <div class="flex items-center space-x-2 mb-3">
                                        <?php if ($is_custom): ?>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                Custom Field
                                            </span>
                                        <?php elseif ($is_modified): ?>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                                Modified
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>

                                <div class="space-y-2">
                                    <div>
                                        <span class="text-xs font-medium text-gray-700">Patterns:</span>
                                        <div class="flex flex-wrap gap-1 mt-1">
                                            <?php foreach (array_slice($definition['patterns'], 0, 3) as $pattern): ?>
                                                <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-blue-100 text-blue-800">
                                                    <?= htmlspecialchars($pattern) ?>
                                                </span>
                                            <?php endforeach; ?>
                                            <?php if (count($definition['patterns']) > 3): ?>
                                                <span class="text-xs text-gray-500">+<?= count($definition['patterns']) - 3 ?> more</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <?php if ($definition['matching']['enabled'] ?? false): ?>
                                        <div class="flex items-center text-xs">
                                            <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                                            <span class="text-green-700">Enabled for matching (Priority: <?= $definition['matching']['priority'] ?>)</span>
                                        </div>
                                    <?php else: ?>
                                        <div class="flex items-center text-xs">
                                            <span class="w-2 h-2 bg-gray-400 rounded-full mr-2"></span>
                                            <span class="text-gray-500">Not used for matching</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
